/**
 * 应用状态管理
 * T016: App版本检查
 * T017: 下载更新功能
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import Constants from 'expo-constants';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

// 版本信息接口
export interface VersionInfo {
  version: string;
  buildNumber: string;
  updateUrl?: string;
  updateDescription?: string;
  forceUpdate: boolean;
  releaseDate: string;
}

// 更新状态
export type UpdateStatus = 'checking' | 'available' | 'downloading' | 'ready' | 'error' | 'none';

// 应用状态接口
export interface AppState {
  // 版本信息
  currentVersion: string;
  currentBuildNumber: string;
  latestVersion: VersionInfo | null;
  
  // 更新状态
  updateStatus: UpdateStatus;
  updateProgress: number;
  updateError: string | null;
  
  // 应用设置
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  
  // 网络状态
  isOnline: boolean;
  
  // 启动状态
  isFirstLaunch: boolean;
  lastLaunchTime: string | null;
}

// 应用Store接口
interface AppStore extends AppState {
  // 版本检查相关
  checkForUpdates: () => Promise<VersionInfo | null>;
  downloadUpdate: () => Promise<void>;
  installUpdate: () => Promise<void>;
  skipUpdate: (version: string) => void;
  clearUpdateError: () => void;
  
  // 设置相关
  setTheme: (theme: 'light' | 'dark' | 'auto') => void;
  setLanguage: (language: 'zh-CN' | 'en-US') => void;
  
  // 网络状态
  setOnlineStatus: (isOnline: boolean) => void;
  
  // 启动相关
  markLaunched: () => void;
  
  // 重置状态
  reset: () => void;
}

// Mock版本数据
const mockVersionInfo: VersionInfo = {
  version: '2.1.1',
  buildNumber: '202412200001',
  updateUrl: 'https://example.com/app-release.apk',
  updateDescription: '本次更新包含以下内容：\n1. 修复了住户管理中的若干问题\n2. 优化了门禁录入流程\n3. 提升了应用性能和稳定性\n4. 新增了批量操作功能',
  forceUpdate: false,
  releaseDate: '2024-12-20',
};

export const useAppStore = create<AppStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      currentVersion: Constants.expoConfig?.version || '1.0.0',
      currentBuildNumber: Constants.expoConfig?.extra?.buildNumber || '1',
      latestVersion: null,
      updateStatus: 'none',
      updateProgress: 0,
      updateError: null,
      theme: 'auto',
      language: 'zh-CN',
      isOnline: true,
      isFirstLaunch: true,
      lastLaunchTime: null,

      // 检查更新
      checkForUpdates: async (): Promise<VersionInfo | null> => {
        set({ updateStatus: 'checking', updateError: null });
        
        try {
          // TODO: 替换为实际的API调用
          // const response = await updateService.checkVersion();
          
          // Mock延迟
          await new Promise(resolve => setTimeout(resolve, 1500));
          
          const currentVersion = get().currentVersion;
          
          // 模拟版本比较
          const needsUpdate = compareVersions(mockVersionInfo.version, currentVersion) > 0;
          
          if (needsUpdate) {
            set({ 
              latestVersion: mockVersionInfo,
              updateStatus: 'available' 
            });
            return mockVersionInfo;
          } else {
            set({ updateStatus: 'none' });
            return null;
          }
        } catch (error) {
          set({ 
            updateStatus: 'error',
            updateError: error instanceof Error ? error.message : '检查更新失败'
          });
          throw error;
        }
      },

      // 下载更新
      downloadUpdate: async (): Promise<void> => {
        const latestVersion = get().latestVersion;
        if (!latestVersion) {
          throw new Error('没有可用的更新');
        }

        set({ updateStatus: 'downloading', updateProgress: 0, updateError: null });

        try {
          // TODO: 替换为实际的下载逻辑
          // 模拟下载进度
          for (let progress = 0; progress <= 100; progress += 10) {
            await new Promise(resolve => setTimeout(resolve, 200));
            set({ updateProgress: progress });
          }

          set({ updateStatus: 'ready' });
        } catch (error) {
          set({ 
            updateStatus: 'error',
            updateError: error instanceof Error ? error.message : '下载失败'
          });
          throw error;
        }
      },

      // 安装更新
      installUpdate: async (): Promise<void> => {
        try {
          // TODO: 替换为实际的安装逻辑
          // 在React Native中，通常需要重启应用或跳转到应用商店
          console.log('Installing update...');
          
          // 模拟安装
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // 重置更新状态
          set({ 
            updateStatus: 'none',
            latestVersion: null,
            updateProgress: 0 
          });
        } catch (error) {
          set({ 
            updateStatus: 'error',
            updateError: error instanceof Error ? error.message : '安装失败'
          });
          throw error;
        }
      },

      // 跳过更新
      skipUpdate: (_version: string) => {
        // TODO: 记录跳过的版本，避免重复提示
        set({
          updateStatus: 'none',
          latestVersion: null
        });
      },

      // 清除更新错误
      clearUpdateError: () => {
        set({ updateError: null });
      },

      // 设置主题
      setTheme: (theme) => {
        set({ theme });
      },

      // 设置语言
      setLanguage: (language) => {
        set({ language });
      },

      // 设置网络状态
      setOnlineStatus: (isOnline) => {
        set({ isOnline });
      },

      // 标记已启动
      markLaunched: () => {
        set({ 
          isFirstLaunch: false,
          lastLaunchTime: new Date().toISOString()
        });
      },

      // 重置状态
      reset: () => {
        set({
          latestVersion: null,
          updateStatus: 'none',
          updateProgress: 0,
          updateError: null,
          theme: 'auto',
          language: 'zh-CN',
          isOnline: true,
          isFirstLaunch: true,
          lastLaunchTime: null,
        });
      },
    }),
    {
      name: 'app-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        theme: state.theme,
        language: state.language,
        isFirstLaunch: state.isFirstLaunch,
        lastLaunchTime: state.lastLaunchTime,
      }),
    }
  )
);

/**
 * 比较版本号
 * @param version1 版本1
 * @param version2 版本2
 * @returns 1: version1 > version2, 0: 相等, -1: version1 < version2
 */
function compareVersions(version1: string, version2: string): number {
  const v1Parts = version1.split('.').map(Number);
  const v2Parts = version2.split('.').map(Number);
  
  const maxLength = Math.max(v1Parts.length, v2Parts.length);
  
  for (let i = 0; i < maxLength; i++) {
    const v1Part = v1Parts[i] || 0;
    const v2Part = v2Parts[i] || 0;
    
    if (v1Part > v2Part) return 1;
    if (v1Part < v2Part) return -1;
  }
  
  return 0;
}
