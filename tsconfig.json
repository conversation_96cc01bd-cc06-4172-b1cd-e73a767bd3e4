{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "allowUnusedLabels": false, "allowUnreachableCode": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "paths": {"@/*": ["./*"], "@/components/*": ["./components/*"], "@/lib/*": ["./lib/*"], "@/services/*": ["./lib/services/*"], "@/stores/*": ["./lib/stores/*"], "@/utils/*": ["./lib/utils/*"], "@/types/*": ["./lib/types/*"], "@/constants/*": ["./lib/constants/*", "./constants/*"], "@/hooks/*": ["./hooks/*"], "@/assets/*": ["./assets/*"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"], "exclude": ["node_modules", "dist", "build", ".expo"]}