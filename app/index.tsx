import { Redirect } from 'expo-router';
import { useEffect } from 'react';
import { StyleSheet, View } from 'react-native';

import { Loading } from '@/components/ui';
import { useAuthStore } from '@/lib/stores/auth.store';

export default function Index() {
  const { isAuthenticated, loginLoading, checkAuthStatus } = useAuthStore();

  useEffect(() => {
    // Check authentication status when app starts
    checkAuthStatus();
  }, [checkAuthStatus]);

  // Show loading while checking authentication
  if (loginLoading) {
    return (
      <View style={styles.container}>
        <Loading visible={true} text='正在检查登录状态...' />
      </View>
    );
  }

  // Redirect based on authentication status
  if (isAuthenticated) {
    return <Redirect href='/(tabs)' />;
  } else {
    return <Redirect href='/(auth)/login' />;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
