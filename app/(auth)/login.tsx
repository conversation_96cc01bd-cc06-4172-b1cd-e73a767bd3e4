/**
 * 登录页面
 * T011: 登录页面 - LoginScreen
 */

import { CustomButton } from '@/components/common/buttons';
import { CountdownButton } from '@/components/common/forms';
import { LoadingSpinner } from '@/components/common/indicators';
import { Screen } from '@/components/common/layout';
import { authService } from '@/lib/services/auth.service';
import { useAuthStore } from '@/lib/stores/auth.store';
import { yupResolver } from '@hookform/resolvers/yup';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { Alert, Keyboard } from 'react-native';
import {
    Button,
    Image,
    Input,
    ScrollView,
    Separator,
    styled,
    Text,
    XStack,
    YStack,
} from 'tamagui';
import * as yup from 'yup';

// 表单验证规则
const loginSchema = yup.object({
  phone: yup
    .string()
    .required('请输入手机号')
    .matches(/^1[3-9]\d{9}$/, '请输入正确的手机号'),
  verificationCode: yup
    .string()
    .required('请输入验证码')
    .length(6, '验证码为6位数字'),
});

type LoginFormData = yup.InferType<typeof loginSchema>;

// 样式化组件
const LoginContainer = styled(YStack, {
  flex: 1,
  backgroundColor: '$background',
  paddingHorizontal: '$4',
});

const LogoContainer = styled(YStack, {
  alignItems: 'center',
  paddingVertical: '$8',
  marginTop: '$8',
});

const FormContainer = styled(YStack, {
  gap: '$4',
  paddingHorizontal: '$2',
});

const InputContainer = styled(YStack, {
  gap: '$2',
});

const PhoneInputContainer = styled(XStack, {
  alignItems: 'center',
  borderWidth: 1,
  borderColor: '$borderColor',
  borderRadius: '$4',
  paddingHorizontal: '$3',
  backgroundColor: '$background',
  minHeight: 48,
});

const VerificationContainer = styled(XStack, {
  gap: '$3',
  alignItems: 'flex-end',
});

const VerificationInputContainer = styled(YStack, {
  flex: 1,
  gap: '$2',
});

const FooterContainer = styled(YStack, {
  gap: '$4',
  marginTop: '$6',
});

const BindAdminLink = styled(XStack, {
  justifyContent: 'center',
  alignItems: 'center',
  paddingVertical: '$3',
});

export default function LoginScreen() {
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);
  const { login, loginLoading, loginError, clearError } = useAuthStore();

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors, isValid },
  } = useForm<LoginFormData>({
    resolver: yupResolver(loginSchema),
    mode: 'onChange',
    defaultValues: {
      phone: '',
      verificationCode: '',
    },
  });

  const phoneValue = watch('phone');

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      setKeyboardVisible(true);
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setKeyboardVisible(false);
    });

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, []);

  useEffect(() => {
    if (loginError) {
      Alert.alert('登录失败', loginError, [
        { text: '确定', onPress: clearError },
      ]);
    }
  }, [loginError, clearError]);

  // 发送验证码
  const handleSendSms = async (): Promise<boolean> => {
    if (!phoneValue || !/^1[3-9]\d{9}$/.test(phoneValue)) {
      Alert.alert('提示', '请输入正确的手机号');
      return false;
    }

    try {
      await authService.sendSms({ phone: phoneValue, type: 'login' });
      Alert.alert('提示', '验证码已发送');
      return true;
    } catch (error) {
      Alert.alert('发送失败', error instanceof Error ? error.message : '发送验证码失败');
      return false;
    }
  };

  // 登录提交
  const onSubmit = async (data: LoginFormData) => {
    try {
      await login({
        phone: data.phone,
        verificationCode: data.verificationCode,
      });
      
      // 登录成功，导航到主页面
      router.replace('/(tabs)');
    } catch (error) {
      // 错误已在store中处理
    }
  };

  // 跳转到绑定管理员页面
  const handleBindAdmin = () => {
    router.push('/(auth)/bind-admin');
  };

  return (
    <Screen>
      <ScrollView
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        <LoginContainer>
          {/* Logo区域 */}
          {!isKeyboardVisible && (
            <LogoContainer>
              <Image
                source={{ uri: 'https://via.placeholder.com/120x120?text=LOGO' }}
                width={120}
                height={120}
                borderRadius="$6"
                marginBottom="$4"
              />
              <Text fontSize="$6" fontWeight="bold" color="$color">
                攸家物管
              </Text>
              <Text fontSize="$4" color="$placeholderColor" marginTop="$2">
                智能物业管理系统
              </Text>
            </LogoContainer>
          )}

          {/* 表单区域 */}
          <FormContainer>
            {/* 手机号输入 */}
            <InputContainer>
              <Text fontSize="$4" fontWeight="500" color="$color">
                手机号
              </Text>
              <Controller
                control={control}
                name="phone"
                render={({ field: { onChange, onBlur, value } }) => (
                  <PhoneInputContainer
                    borderColor={errors.phone ? '$error' : '$borderColor'}
                  >
                    <Text fontSize="$4" color="$placeholderColor" marginRight="$2">
                      +86
                    </Text>
                    <Separator vertical height={20} />
                    <Input
                      flex={1}
                      placeholder="请输入手机号"
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      keyboardType="phone-pad"
                      maxLength={11}
                      borderWidth={0}
                      backgroundColor="transparent"
                      fontSize="$4"
                    />
                  </PhoneInputContainer>
                )}
              />
              {errors.phone && (
                <Text fontSize="$3" color="$error">
                  {errors.phone.message}
                </Text>
              )}
            </InputContainer>

            {/* 验证码输入 */}
            <VerificationContainer>
              <VerificationInputContainer>
                <Text fontSize="$4" fontWeight="500" color="$color">
                  验证码
                </Text>
                <Controller
                  control={control}
                  name="verificationCode"
                  render={({ field: { onChange, onBlur, value } }) => (
                    <Input
                      placeholder="请输入验证码"
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      keyboardType="number-pad"
                      maxLength={6}
                      borderColor={errors.verificationCode ? '$error' : '$borderColor'}
                      fontSize="$4"
                      height={48}
                    />
                  )}
                />
                {errors.verificationCode && (
                  <Text fontSize="$3" color="$error">
                    {errors.verificationCode.message}
                  </Text>
                )}
              </VerificationInputContainer>

              <CountdownButton
                onPress={handleSendSms}
                disabled={!phoneValue || !/^1[3-9]\d{9}$/.test(phoneValue)}
                countdown={60}
                text="获取验证码"
                countdownText="重新发送"
                width={100}
                height={48}
              />
            </VerificationContainer>

            {/* 登录按钮 */}
            <FooterContainer>
              <CustomButton
                onPress={handleSubmit(onSubmit)}
                disabled={!isValid || loginLoading}
                variant="primary"
                size="large"
                width="100%"
              >
                {loginLoading ? (
                  <XStack alignItems="center" gap="$2">
                    <LoadingSpinner size="small" color="white" />
                    <Text color="white" fontSize="$4" fontWeight="500">
                      登录中...
                    </Text>
                  </XStack>
                ) : (
                  <Text color="white" fontSize="$4" fontWeight="500">
                    登录
                  </Text>
                )}
              </CustomButton>

              {/* 绑定管理员入口 */}
              <BindAdminLink>
                <Text fontSize="$3" color="$placeholderColor">
                  还没有账号？
                </Text>
                <Button
                  variant="outlined"
                  size="$2"
                  onPress={handleBindAdmin}
                  marginLeft="$2"
                  borderColor="$primary"
                  backgroundColor="transparent"
                >
                  <Text color="$primary" fontSize="$3">
                    绑定管理员
                  </Text>
                </Button>
              </BindAdminLink>
            </FooterContainer>
          </FormContainer>
        </LoginContainer>
      </ScrollView>
    </Screen>
  );
}
