/**
 * 绑定成功页面
 * T013: 绑定成功页面 - BindSuccessScreen
 */

import { CustomButton } from '@/components/common/buttons';
import { Screen } from '@/components/common/layout';
import { useAuthStore } from '@/lib/stores/auth.store';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React from 'react';
import {
    styled,
    Text,
    XStack,
    YStack
} from 'tamagui';

// 样式化组件
const SuccessContainer = styled(YStack, {
  flex: 1,
  backgroundColor: '$background',
  paddingHorizontal: '$4',
  justifyContent: 'center',
  alignItems: 'center',
});

const IconContainer = styled(YStack, {
  alignItems: 'center',
  marginBottom: '$6',
});

const SuccessIcon = styled(YStack, {
  width: 80,
  height: 80,
  borderRadius: 40,
  backgroundColor: '$success',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: '$4',
});

const ContentContainer = styled(YStack, {
  alignItems: 'center',
  gap: '$4',
  marginBottom: '$8',
});

const UserInfoContainer = styled(YStack, {
  backgroundColor: '$backgroundHover',
  padding: '$4',
  borderRadius: '$4',
  width: '100%',
  gap: '$3',
});

const InfoRow = styled(XStack, {
  justifyContent: 'space-between',
  alignItems: 'center',
});

const InstructionContainer = styled(YStack, {
  backgroundColor: '$backgroundHover',
  padding: '$4',
  borderRadius: '$4',
  width: '100%',
  gap: '$2',
  marginBottom: '$6',
});

const ButtonContainer = styled(YStack, {
  width: '100%',
  gap: '$3',
});

export default function BindSuccessScreen() {
  const { user } = useAuthStore();
  const router = useRouter();

  // 跳转到主页面
  const handleGoToMain = () => {
    router.replace('/(tabs)');
  };

  // 格式化手机号显示
  const formatPhone = (phone: string) => {
    if (!phone) return '';
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  };

  return (
    <Screen>
      <SuccessContainer>
        {/* 成功图标 */}
        <IconContainer>
          <SuccessIcon>
            <Ionicons name="checkmark" size={40} color="white" />
          </SuccessIcon>
          <Text fontSize="$6" fontWeight="bold" color="$color">
            绑定成功！
          </Text>
          <Text fontSize="$4" color="$placeholderColor" textAlign="center" marginTop="$2">
            恭喜您成功绑定管理员账号
          </Text>
        </IconContainer>

        {/* 用户信息展示 */}
        <ContentContainer>
          <UserInfoContainer>
            <Text fontSize="$4" fontWeight="500" color="$color" marginBottom="$2">
              管理员信息
            </Text>
            
            <InfoRow>
              <Text fontSize="$3" color="$placeholderColor">
                姓名
              </Text>
              <Text fontSize="$3" color="$color" fontWeight="500">
                {user?.name || '未知'}
              </Text>
            </InfoRow>
            
            <InfoRow>
              <Text fontSize="$3" color="$placeholderColor">
                手机号
              </Text>
              <Text fontSize="$3" color="$color" fontWeight="500">
                {formatPhone(user?.phone || '')}
              </Text>
            </InfoRow>
            
            <InfoRow>
              <Text fontSize="$3" color="$placeholderColor">
                所属小区
              </Text>
              <Text fontSize="$3" color="$color" fontWeight="500">
                {user?.community?.name || '未知小区'}
              </Text>
            </InfoRow>
            
            <InfoRow>
              <Text fontSize="$3" color="$placeholderColor">
                角色权限
              </Text>
              <Text fontSize="$3" color="$primary" fontWeight="500">
                管理员
              </Text>
            </InfoRow>
          </UserInfoContainer>

          {/* 使用说明 */}
          <InstructionContainer>
            <Text fontSize="$4" fontWeight="500" color="$color" marginBottom="$2">
              使用说明
            </Text>
            
            <XStack alignItems="flex-start" gap="$2">
              <Text fontSize="$3" color="$primary" marginTop="$1">
                •
              </Text>
              <Text fontSize="$3" color="$placeholderColor" flex={1} lineHeight="$1">
                您现在可以使用手机号和验证码登录系统
              </Text>
            </XStack>
            
            <XStack alignItems="flex-start" gap="$2">
              <Text fontSize="$3" color="$primary" marginTop="$1">
                •
              </Text>
              <Text fontSize="$3" color="$placeholderColor" flex={1} lineHeight="$1">
                作为管理员，您拥有住户管理、员工管理等全部权限
              </Text>
            </XStack>
            
            <XStack alignItems="flex-start" gap="$2">
              <Text fontSize="$3" color="$primary" marginTop="$1">
                •
              </Text>
              <Text fontSize="$3" color="$placeholderColor" flex={1} lineHeight="$1">
                建议您先完善个人门禁信息，设置密码和录入人像
              </Text>
            </XStack>
            
            <XStack alignItems="flex-start" gap="$2">
              <Text fontSize="$3" color="$primary" marginTop="$1">
                •
              </Text>
              <Text fontSize="$3" color="$placeholderColor" flex={1} lineHeight="$1">
                如有问题，请联系物业公司技术支持
              </Text>
            </XStack>
          </InstructionContainer>
        </ContentContainer>

        {/* 操作按钮 */}
        <ButtonContainer>
          <CustomButton
            onPress={handleGoToMain}
            variant="primary"
            size="large"
            width="100%"
          >
            <Text color="white" fontSize="$4" fontWeight="500">
              我知道了并登录
            </Text>
          </CustomButton>
        </ButtonContainer>
      </SuccessContainer>
    </Screen>
  );
}
