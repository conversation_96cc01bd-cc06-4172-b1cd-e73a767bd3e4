/**
 * 绑定管理员页面
 * T012: 绑定管理员页面 - BindAdminScreen
 */

import { CustomButton } from '@/components/common/buttons';
import { CountdownButton } from '@/components/common/forms';
import { LoadingSpinner } from '@/components/common/indicators';
import { Header, Screen } from '@/components/common/layout';
import { authService } from '@/lib/services/auth.service';
import { useAuthStore } from '@/lib/stores/auth.store';
import { yupResolver } from '@hookform/resolvers/yup';
import { router } from 'expo-router';
import React, { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { Alert } from 'react-native';
import {
    Input,
    ScrollView,
    Separator,
    styled,
    Text,
    XStack,
    YStack,
} from 'tamagui';
import * as yup from 'yup';

// 表单验证规则
const bindAdminSchema = yup.object({
  dynamicCode: yup
    .string()
    .required('请输入动态口令')
    .min(6, '动态口令至少6位'),
  password: yup
    .string()
    .required('请输入口令密码')
    .min(6, '口令密码至少6位'),
  name: yup
    .string()
    .required('请输入姓名')
    .min(2, '姓名至少2个字符')
    .max(20, '姓名不能超过20个字符'),
  phone: yup
    .string()
    .required('请输入手机号')
    .matches(/^1[3-9]\d{9}$/, '请输入正确的手机号'),
  verificationCode: yup
    .string()
    .required('请输入验证码')
    .length(6, '验证码为6位数字'),
});

type BindAdminFormData = yup.InferType<typeof bindAdminSchema>;

// 样式化组件
const BindAdminContainer = styled(YStack, {
  flex: 1,
  backgroundColor: '$background',
});

const ContentContainer = styled(YStack, {
  flex: 1,
  paddingHorizontal: '$4',
  paddingTop: '$4',
});

const FormContainer = styled(YStack, {
  gap: '$4',
});

const InputContainer = styled(YStack, {
  gap: '$2',
});

const PhoneInputContainer = styled(XStack, {
  alignItems: 'center',
  borderWidth: 1,
  borderColor: '$borderColor',
  borderRadius: '$4',
  paddingHorizontal: '$3',
  backgroundColor: '$background',
  minHeight: 48,
});

const VerificationContainer = styled(XStack, {
  gap: '$3',
  alignItems: 'flex-end',
});

const VerificationInputContainer = styled(YStack, {
  flex: 1,
  gap: '$2',
});

const FooterContainer = styled(YStack, {
  gap: '$4',
  marginTop: '$6',
  paddingBottom: '$4',
});

const TipContainer = styled(YStack, {
  backgroundColor: '$backgroundHover',
  padding: '$3',
  borderRadius: '$4',
  marginBottom: '$4',
});

export default function BindAdminScreen() {

  const { bindAdmin, bindAdminLoading, bindAdminError, clearError } = useAuthStore();

  const {
    control,
    handleSubmit,
    watch,
    formState: { errors, isValid },
  } = useForm<BindAdminFormData>({
    resolver: yupResolver(bindAdminSchema),
    mode: 'onChange',
    defaultValues: {
      dynamicCode: '',
      password: '',
      name: '',
      phone: '',
      verificationCode: '',
    },
  });

  const phoneValue = watch('phone');



  useEffect(() => {
    if (bindAdminError) {
      Alert.alert('绑定失败', bindAdminError, [
        { text: '确定', onPress: clearError },
      ]);
    }
  }, [bindAdminError, clearError]);

  // 返回登录页面
  const handleGoBack = () => {
    router.back();
  };

  // 发送验证码
  const handleSendSms = async (): Promise<boolean> => {
    if (!phoneValue || !/^1[3-9]\d{9}$/.test(phoneValue)) {
      Alert.alert('提示', '请输入正确的手机号');
      return false;
    }

    try {
      await authService.sendSms({ phone: phoneValue, type: 'bind' });
      Alert.alert('提示', '验证码已发送');
      return true;
    } catch (error) {
      Alert.alert('发送失败', error instanceof Error ? error.message : '发送验证码失败');
      return false;
    }
  };

  // 绑定提交
  const onSubmit = async (data: BindAdminFormData) => {
    try {
      await bindAdmin({
        dynamicCode: data.dynamicCode,
        password: data.password,
        name: data.name,
        phone: data.phone,
        verificationCode: data.verificationCode,
      });
      
      // 绑定成功，跳转到成功页面
      router.push('/(tabs)/auth/bind-success');
    } catch (error) {
      // 错误已在store中处理
    }
  };

  return (
    <Screen>
      <BindAdminContainer>
        {/* 头部 */}
        <Header
          title="绑定管理员"
          leftIcon="arrow-back"
          leftAction={handleGoBack}
        />

        <ScrollView
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <ContentContainer>
            {/* 提示信息 */}
            <TipContainer>
              <Text fontSize="$3" color="$placeholderColor" lineHeight="$1">
                请联系物业公司获取动态口令和口令密码，完成管理员账号绑定
              </Text>
            </TipContainer>

            {/* 表单区域 */}
            <FormContainer>
              {/* 动态口令 */}
              <InputContainer>
                <Text fontSize="$4" fontWeight="500" color="$color">
                  动态口令 *
                </Text>
                <Controller
                  control={control}
                  name="dynamicCode"
                  render={({ field: { onChange, onBlur, value } }) => (
                    <Input
                      placeholder="请输入动态口令"
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      borderColor={errors.dynamicCode ? '$error' : '$borderColor'}
                      fontSize="$4"
                      height={48}
                    />
                  )}
                />
                {errors.dynamicCode && (
                  <Text fontSize="$3" color="$error">
                    {errors.dynamicCode.message}
                  </Text>
                )}
              </InputContainer>

              {/* 口令密码 */}
              <InputContainer>
                <Text fontSize="$4" fontWeight="500" color="$color">
                  口令密码 *
                </Text>
                <Controller
                  control={control}
                  name="password"
                  render={({ field: { onChange, onBlur, value } }) => (
                    <Input
                      placeholder="请输入口令密码"
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      secureTextEntry
                      borderColor={errors.password ? '$error' : '$borderColor'}
                      fontSize="$4"
                      height={48}
                    />
                  )}
                />
                {errors.password && (
                  <Text fontSize="$3" color="$error">
                    {errors.password.message}
                  </Text>
                )}
              </InputContainer>

              {/* 姓名 */}
              <InputContainer>
                <Text fontSize="$4" fontWeight="500" color="$color">
                  姓名 *
                </Text>
                <Controller
                  control={control}
                  name="name"
                  render={({ field: { onChange, onBlur, value } }) => (
                    <Input
                      placeholder="请输入真实姓名"
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      borderColor={errors.name ? '$error' : '$borderColor'}
                      fontSize="$4"
                      height={48}
                    />
                  )}
                />
                {errors.name && (
                  <Text fontSize="$3" color="$error">
                    {errors.name.message}
                  </Text>
                )}
              </InputContainer>

              {/* 手机号 */}
              <InputContainer>
                <Text fontSize="$4" fontWeight="500" color="$color">
                  手机号 *
                </Text>
                <Controller
                  control={control}
                  name="phone"
                  render={({ field: { onChange, onBlur, value } }) => (
                    <PhoneInputContainer
                      borderColor={errors.phone ? '$error' : '$borderColor'}
                    >
                      <Text fontSize="$4" color="$placeholderColor" marginRight="$2">
                        +86
                      </Text>
                      <Separator vertical height={20} />
                      <Input
                        flex={1}
                        placeholder="请输入手机号"
                        value={value}
                        onChangeText={onChange}
                        onBlur={onBlur}
                        keyboardType="phone-pad"
                        maxLength={11}
                        borderWidth={0}
                        backgroundColor="transparent"
                        fontSize="$4"
                      />
                    </PhoneInputContainer>
                  )}
                />
                {errors.phone && (
                  <Text fontSize="$3" color="$error">
                    {errors.phone.message}
                  </Text>
                )}
              </InputContainer>

              {/* 验证码 */}
              <VerificationContainer>
                <VerificationInputContainer>
                  <Text fontSize="$4" fontWeight="500" color="$color">
                    短信验证码 *
                  </Text>
                  <Controller
                    control={control}
                    name="verificationCode"
                    render={({ field: { onChange, onBlur, value } }) => (
                      <Input
                        placeholder="请输入验证码"
                        value={value}
                        onChangeText={onChange}
                        onBlur={onBlur}
                        keyboardType="number-pad"
                        maxLength={6}
                        borderColor={errors.verificationCode ? '$error' : '$borderColor'}
                        fontSize="$4"
                        height={48}
                      />
                    )}
                  />
                  {errors.verificationCode && (
                    <Text fontSize="$3" color="$error">
                      {errors.verificationCode.message}
                    </Text>
                  )}
                </VerificationInputContainer>

                <CountdownButton
                  onPress={handleSendSms}
                  disabled={!phoneValue || !/^1[3-9]\d{9}$/.test(phoneValue)}
                  countdown={60}
                  text="获取验证码"
                  countdownText="重新发送"
                  width={100}
                  height={48}
                />
              </VerificationContainer>
            </FormContainer>

            {/* 确认绑定按钮 */}
            <FooterContainer>
              <CustomButton
                onPress={handleSubmit(onSubmit)}
                disabled={!isValid || bindAdminLoading}
                variant="primary"
                size="large"
                width="100%"
              >
                {bindAdminLoading ? (
                  <XStack alignItems="center" gap="$2">
                    <LoadingSpinner size="small" color="white" />
                    <Text color="white" fontSize="$4" fontWeight="500">
                      绑定中...
                    </Text>
                  </XStack>
                ) : (
                  <Text color="white" fontSize="$4" fontWeight="500">
                    确认绑定
                  </Text>
                )}
              </CustomButton>
            </FooterContainer>
          </ContentContainer>
        </ScrollView>
      </BindAdminContainer>
    </Screen>
  );
}
