/**
 * 认证页面布局
 */

import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React from 'react';

export default function AuthLayout() {

  return (
    <>
      <StatusBar style="dark" />
      <Stack
        screenOptions={{
          headerShown: false,
          gestureEnabled: false,
          animation: 'slide_from_right',
        }}
      >
        <Stack.Screen
          name="login"
          options={{
            title: '登录',
          }}
        />
        <Stack.Screen
          name="bind-admin"
          options={{
            title: '绑定管理员',
          }}
        />
        <Stack.Screen
          name="bind-success"
          options={{
            title: '绑定成功',
            gestureEnabled: false,
          }}
        />
      </Stack>
    </>
  );
}
