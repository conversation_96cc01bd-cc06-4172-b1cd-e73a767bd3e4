import { Tabs, useRouter } from 'expo-router';
import React, { useEffect } from 'react';
import { Platform } from 'react-native';

import { HapticTab } from '@/components/HapticTab';
import { IconSymbol } from '@/components/ui/IconSymbol';
import TabBarBackground from '@/components/ui/TabBarBackground';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useAuthStore } from '@/lib/stores/auth.store';

export default function TabLayout() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const { isAuthenticated } = useAuthStore();

  // 检查认证状态，未认证时重定向到登录页面
  useEffect(() => {
    if (!isAuthenticated) {
      router.replace('/(tabs)/auth/login');
    }
  }, [isAuthenticated, router]);

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: Colors[colorScheme ?? 'light'].tint,
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: isAuthenticated ? Platform.select({
          ios: {
            // Use a transparent background on iOS to show the blur effect
            position: 'absolute',
          },
          default: {},
        }) : { display: 'none' }, // 未认证时隐藏tab bar
      }}
      initialRouteName={isAuthenticated ? "index" : "auth"}
    >
      {/* 认证页面 - 始终存在，但根据认证状态控制显示 */}
      <Tabs.Screen
        name="auth"
        options={{
          title: '登录',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="person.fill" color={color} />,
          href: isAuthenticated ? null : '/(tabs)/auth', // 已认证时隐藏此tab
        }}
      />

      {/* 主要功能页面 - 只有认证后才显示 */}
      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="house.fill" color={color} />,
          href: isAuthenticated ? '/(tabs)' : null, // 未认证时隐藏此tab
        }}
      />
      <Tabs.Screen
        name="explore"
        options={{
          title: 'Explore',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="paperplane.fill" color={color} />,
          href: isAuthenticated ? '/(tabs)/explore' : null, // 未认证时隐藏此tab
        }}
      />
    </Tabs>
  );
}
