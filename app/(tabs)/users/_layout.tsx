import { Stack } from 'expo-router';

import { theme } from '@/config/theme';

export default function UsersLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: false,
        contentStyle: { backgroundColor: theme.colors.background },
      }}
    >
      <Stack.Screen name='index' />
      <Stack.Screen name='residents' />
      <Stack.Screen name='staff' />
    </Stack>
  );
}
