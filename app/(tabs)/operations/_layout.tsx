import { Stack } from 'expo-router';

import { theme } from '@/config/theme';

export default function OperationsLayout() {
  return (
    <Stack
      screenOptions={{
        headerShown: false,
        contentStyle: { backgroundColor: theme.colors.background },
      }}
    >
      <Stack.Screen name='index' />
      <Stack.Screen name='device-register' />
      <Stack.Screen name='device-debug' />
      <Stack.Screen name='device-unbind' />
      <Stack.Screen name='device-control' />
    </Stack>
  );
}
