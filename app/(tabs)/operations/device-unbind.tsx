import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
    Al<PERSON>,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { LocationPath, LocationPicker, QRScanner } from '@/components/business';
import { Button, Input } from '@/components/ui';
import { theme } from '@/config/theme';
import { MockDevice, mockDevices, mockLocationData } from '@/data/mockData';

type DeviceType = 'door' | 'gateway' | 'caller';

export default function DeviceUnbindScreen() {
  const [selectedDevice, setSelectedDevice] = useState<MockDevice | null>(null);
  const [selectedDeviceType, setSelectedDeviceType] = useState<DeviceType>('door');
  const [showQRScanner, setShowQRScanner] = useState(false);
  const [showLocationPicker, setShowLocationPicker] = useState(false);
  const [unbindReason, setUnbindReason] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // 设备类型选项
  const deviceTypes = [
    { type: 'door' as DeviceType, name: '门禁', icon: 'lock-closed' },
    { type: 'gateway' as DeviceType, name: '网关', icon: 'wifi' },
    { type: 'caller' as DeviceType, name: '呼叫器', icon: 'call' },
  ];

  // 处理二维码扫描结果
  const handleQRScanSuccess = (data: string) => {
    const device = mockDevices.find(d => d.deviceNumber === data);
    if (device) {
      setSelectedDevice(device);
      // 根据设备类型自动选择对应的类型按钮
      setSelectedDeviceType(device.type);
    } else {
      Alert.alert('错误', '未找到设备信息，请确认设备已注册');
    }
    setShowQRScanner(false);
  };

  // 处理位置选择
  const handleLocationSelect = (path: LocationPath[]) => {
    const locationStr = path.map(item => item.name).join(' ');
    const device = mockDevices.find(d => d.location === locationStr);
    if (device) {
      setSelectedDevice(device);
      setSelectedDeviceType(device.type);
    } else {
      Alert.alert('提示', '当前位置下没有绑定任何设备');
    }
    setShowLocationPicker(false);
  };

  // 处理设备解绑
  const handleUnbindDevice = async () => {
    if (!selectedDevice) {
      Alert.alert('提示', '请先选择要解绑的设备');
      return;
    }

    Alert.alert(
      '确认解绑',
      `确定要解绑设备 "${selectedDevice.deviceNumber}" 吗？\n\n解绑后设备将与当前位置断开关联，此操作不可撤销。`,
      [
        {
          text: '取消',
          style: 'cancel',
        },
        {
          text: '确认解绑',
          style: 'destructive',
          onPress: async () => {
            setIsLoading(true);
            try {
              // 模拟API调用
              await new Promise(resolve => setTimeout(resolve, 1500));

              Alert.alert(
                '解绑成功',
                '设备已成功解绑，可以继续扫描下一个设备进行解绑。',
                [
                  {
                    text: '继续解绑',
                    onPress: () => {
                      setSelectedDevice(null);
                      setUnbindReason('');
                    },
                  },
                  {
                    text: '完成',
                    style: 'default',
                  },
                ]
              );
            } catch (error) {
              Alert.alert('错误', '解绑失败，请稍后重试');
            } finally {
              setIsLoading(false);
            }
          },
        },
      ]
    );
  };

  // 复制设备编号
  const handleCopyDeviceNumber = () => {
    if (selectedDevice) {
      // 在实际应用中，这里会调用剪贴板API
      Alert.alert('提示', '设备编号已复制到剪贴板');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* 头部 */}
        <View style={styles.header}>
          <Text style={styles.title}>设备解绑</Text>
          <Text style={styles.subtitle}>扫码或选择位置来解绑设备</Text>
        </View>

        {/* 设备类型选择 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>设备类型</Text>
          <View style={styles.deviceTypeContainer}>
            {deviceTypes.map((deviceType) => (
              <TouchableOpacity
                key={deviceType.type}
                style={[
                  styles.deviceTypeButton,
                  selectedDeviceType === deviceType.type && styles.deviceTypeButtonSelected,
                ]}
                onPress={() => setSelectedDeviceType(deviceType.type)}
              >
                <Ionicons
                  name={deviceType.icon as any}
                  size={20}
                  color={
                    selectedDeviceType === deviceType.type
                      ? theme.colors.white
                      : theme.colors.primary
                  }
                />
                <Text
                  style={[
                    styles.deviceTypeText,
                    selectedDeviceType === deviceType.type && styles.deviceTypeTextSelected,
                  ]}
                >
                  {deviceType.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* 设备选择 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>选择设备</Text>
          <View style={styles.deviceSelectContainer}>
            <Button
              variant="outline"
              size="medium"
              onPress={() => setShowQRScanner(true)}
              containerStyle={styles.selectButton}
            >
              <Ionicons name="qr-code" size={20} color={theme.colors.primary} />
              <Text style={styles.selectButtonText}>扫描二维码</Text>
            </Button>
            <Button
              variant="outline"
              size="medium"
              onPress={() => setShowLocationPicker(true)}
              containerStyle={styles.selectButton}
            >
              <Ionicons name="location" size={20} color={theme.colors.primary} />
              <Text style={styles.selectButtonText}>选择位置</Text>
            </Button>
          </View>
        </View>

        {/* 设备信息 */}
        {selectedDevice && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>设备信息</Text>
            <View style={styles.deviceInfo}>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>设备类型：</Text>
                <View style={styles.deviceTypeBadge}>
                  <Text style={styles.deviceTypeBadgeText}>
                    {deviceTypes.find(t => t.type === selectedDevice.type)?.name}
                  </Text>
                </View>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>基础设施：</Text>
                <Text style={styles.infoValue}>{selectedDevice.location}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>设备编号：</Text>
                <View style={styles.deviceNumberContainer}>
                  <Text style={styles.infoValue}>{selectedDevice.deviceNumber}</Text>
                  <TouchableOpacity
                    style={styles.copyButton}
                    onPress={handleCopyDeviceNumber}
                  >
                    <Ionicons name="copy" size={16} color={theme.colors.primary} />
                  </TouchableOpacity>
                </View>
              </View>
              {selectedDevice.remark && (
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>备注：</Text>
                  <Text style={styles.infoValue}>{selectedDevice.remark}</Text>
                </View>
              )}
            </View>
          </View>
        )}

        {/* 解绑原因 */}
        {selectedDevice && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>解绑原因（选填）</Text>
            <Input
              placeholder="请根据实际情况输入解绑原因"
              value={unbindReason}
              onChangeText={setUnbindReason}
              multiline
              numberOfLines={3}
              containerStyle={styles.reasonInput}
            />
          </View>
        )}

        {/* 解绑按钮 */}
        {selectedDevice && (
          <View style={styles.section}>
            <Button
              variant="danger"
              size="large"
              onPress={handleUnbindDevice}
              loading={isLoading}
              containerStyle={styles.unbindButton}
            >
              <Ionicons name="unlink" size={20} color={theme.colors.white} />
              <Text style={styles.unbindButtonText}>确认解绑</Text>
            </Button>
          </View>
        )}

        {/* 空状态 */}
        {!selectedDevice && (
          <View style={styles.emptyState}>
            <Ionicons name="unlink-outline" size={64} color={theme.colors.textSecondary} />
            <Text style={styles.emptyStateText}>请先选择要解绑的设备</Text>
            <Text style={styles.emptyStateSubtext}>通过扫码或选择位置来选择设备</Text>
          </View>
        )}
      </ScrollView>

      {/* 二维码扫描器 */}
      <QRScanner
        visible={showQRScanner}
        onScanSuccess={handleQRScanSuccess}
        onClose={() => setShowQRScanner(false)}
        hint="请将设备二维码放入扫描框内"
      />

      {/* 位置选择器 */}
      <LocationPicker
        visible={showLocationPicker}
        title="选择设备位置"
        data={mockLocationData}
        onSelect={handleLocationSelect}
        onCancel={() => setShowLocationPicker(false)}
        maxLevel={4}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  section: {
    backgroundColor: theme.colors.white,
    marginTop: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.lg,
  },
  deviceTypeContainer: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  deviceTypeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.white,
    gap: theme.spacing.sm,
  },
  deviceTypeButtonSelected: {
    backgroundColor: theme.colors.primary,
  },
  deviceTypeText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.primary,
  },
  deviceTypeTextSelected: {
    color: theme.colors.white,
  },
  deviceSelectContainer: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  selectButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  selectButtonText: {
    fontSize: 14,
    color: theme.colors.primary,
    fontWeight: '600',
  },
  deviceInfo: {
    gap: theme.spacing.md,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoLabel: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    minWidth: 80,
  },
  infoValue: {
    fontSize: 16,
    color: theme.colors.text,
    flex: 1,
  },
  deviceTypeBadge: {
    backgroundColor: theme.colors.primaryLight,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.sm,
  },
  deviceTypeBadgeText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.primary,
  },
  deviceNumberContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: theme.spacing.sm,
  },
  copyButton: {
    padding: theme.spacing.xs,
    borderRadius: theme.borderRadius.sm,
    backgroundColor: theme.colors.surface,
  },
  reasonInput: {
    marginTop: 0,
  },
  unbindButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  unbindButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.white,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: theme.spacing.xl * 2,
    gap: theme.spacing.md,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
});
