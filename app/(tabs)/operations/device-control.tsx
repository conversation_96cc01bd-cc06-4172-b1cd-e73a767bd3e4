import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    FlatList,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { LocationPath, LocationPicker } from '@/components/business';
import { Button } from '@/components/ui';
import { theme } from '@/config/theme';
import { MockDevice, mockDevices, mockLocationData } from '@/data/mockData';

type OperationType = 'transition' | 'restart' | 'report' | 'sync';

interface DeviceListItem extends MockDevice {
  selected: boolean;
}

export default function DeviceControlScreen() {
  const [selectedLocation, setSelectedLocation] = useState<LocationPath[]>([]);
  const [showLocationPicker, setShowLocationPicker] = useState(false);
  const [deviceList, setDeviceList] = useState<DeviceListItem[]>([]);
  const [selectedDevices, setSelectedDevices] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // 根据选择的位置筛选设备
  useEffect(() => {
    if (selectedLocation.length > 0) {
      const locationStr = selectedLocation.map(item => item.name).join(' ');
      const filteredDevices = mockDevices
        .filter(device => device.location.includes(locationStr))
        .map(device => ({ ...device, selected: false }));
      setDeviceList(filteredDevices);
      setSelectedDevices([]);
    } else {
      setDeviceList([]);
      setSelectedDevices([]);
    }
  }, [selectedLocation]);

  // 处理位置选择
  const handleLocationSelect = (path: LocationPath[]) => {
    setSelectedLocation(path);
    setShowLocationPicker(false);
  };

  // 清空位置筛选
  const handleClearLocation = () => {
    setSelectedLocation([]);
    setDeviceList([]);
    setSelectedDevices([]);
  };

  // 处理设备选择
  const handleDeviceSelect = (deviceId: string) => {
    setSelectedDevices(prev => {
      if (prev.includes(deviceId)) {
        return prev.filter(id => id !== deviceId);
      } else {
        return [...prev, deviceId];
      }
    });
  };

  // 全选/取消全选
  const handleSelectAll = () => {
    if (selectedDevices.length === deviceList.length) {
      setSelectedDevices([]);
    } else {
      setSelectedDevices(deviceList.map(device => device.id));
    }
  };

  // 批量操作
  const handleBatchOperation = async (operation: OperationType) => {
    if (selectedDevices.length === 0) {
      Alert.alert('提示', '请先选择设备');
      return;
    }

    const operationNames = {
      transition: '过渡模式切换',
      restart: '远程重启',
      report: '上报数据',
      sync: '下发数据',
    };

    const selectedDeviceNames = deviceList
      .filter(device => selectedDevices.includes(device.id))
      .map(device => device.deviceNumber)
      .join('、');

    Alert.alert(
      '确认操作',
      `确定要对以下设备执行"${operationNames[operation]}"操作吗？\n\n${selectedDeviceNames}`,
      [
        {
          text: '取消',
          style: 'cancel',
        },
        {
          text: '确认',
          onPress: async () => {
            setIsLoading(true);
            try {
              // 模拟API调用
              await new Promise(resolve => setTimeout(resolve, 2000));

              // 模拟部分成功的情况
              const successCount = Math.floor(selectedDevices.length * 0.8);
              const failCount = selectedDevices.length - successCount;

              let message = `操作完成！\n成功：${successCount}台`;
              if (failCount > 0) {
                message += `\n失败：${failCount}台（设备离线或其他原因）`;
              }

              Alert.alert('操作结果', message);
            } catch (error) {
              Alert.alert('错误', '操作失败，请稍后重试');
            } finally {
              setIsLoading(false);
            }
          },
        },
      ]
    );
  };

  // 渲染设备列表项
  const renderDeviceItem = ({ item }: { item: DeviceListItem }) => {
    const isSelected = selectedDevices.includes(item.id);
    const isOnline = Math.random() > 0.2; // 模拟在线状态

    return (
      <TouchableOpacity
        style={[styles.deviceItem, isSelected && styles.deviceItemSelected]}
        onPress={() => handleDeviceSelect(item.id)}
        activeOpacity={0.7}
      >
        <View style={styles.deviceItemHeader}>
          <View style={styles.deviceItemLeft}>
            <View style={[styles.statusDot, { backgroundColor: isOnline ? theme.colors.success : theme.colors.error }]} />
            <Text style={styles.deviceLocation}>{item.location}</Text>
          </View>
          <View style={[styles.checkbox, isSelected && styles.checkboxSelected]}>
            {isSelected && (
              <Ionicons name="checkmark" size={16} color={theme.colors.white} />
            )}
          </View>
        </View>

        <View style={styles.deviceItemContent}>
          <View style={styles.deviceInfo}>
            <Text style={styles.deviceNumber}>设备编号：{item.deviceNumber}</Text>
            <Text style={styles.deviceStatus}>
              状态：{isOnline ? '在线' : '离线'}
            </Text>
            <Text style={styles.deviceVersion}>版本：V1.2.3</Text>
            <Text style={styles.deviceTime}>注册时间：2024-01-15 10:30</Text>
          </View>

          <View style={styles.deviceStats}>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>门禁实录</Text>
              <Text style={styles.statValue}>人像:{item.faceCount} 密码:{item.passwordCount} NFC:{item.nfcCount}</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>服务实录</Text>
              <Text style={styles.statValue}>人像:45 密码:12 NFC:23</Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* 头部 */}
      <View style={styles.header}>
        <Text style={styles.title}>设备控制</Text>
        <Text style={styles.subtitle}>按位置筛选设备并执行批量操作</Text>
      </View>

      {/* 位置筛选 */}
      <View style={styles.filterSection}>
        <Text style={styles.sectionTitle}>基础设施筛选</Text>
        <View style={styles.filterContainer}>
          <Button
            variant="outline"
            size="medium"
            onPress={() => setShowLocationPicker(true)}
            containerStyle={styles.filterButton}
          >
            <Ionicons name="location" size={20} color={theme.colors.primary} />
            <Text style={styles.filterButtonText}>
              {selectedLocation.length > 0
                ? selectedLocation.map(item => item.name).join(' > ')
                : '请选择基础设施'
              }
            </Text>
          </Button>
          {selectedLocation.length > 0 && (
            <Button
              variant="outline"
              size="medium"
              onPress={handleClearLocation}
              containerStyle={styles.clearButton}
            >
              <Text style={styles.clearButtonText}>清空</Text>
            </Button>
          )}
        </View>
      </View>

      {/* 设备列表 */}
      {deviceList.length > 0 ? (
        <View style={styles.deviceListSection}>
          <View style={styles.listHeader}>
            <Text style={styles.sectionTitle}>设备列表 ({deviceList.length}台)</Text>
            <TouchableOpacity
              style={styles.selectAllButton}
              onPress={handleSelectAll}
            >
              <Text style={styles.selectAllText}>
                {selectedDevices.length === deviceList.length ? '取消全选' : '全选'}
              </Text>
            </TouchableOpacity>
          </View>

          <FlatList
            data={deviceList}
            renderItem={renderDeviceItem}
            keyExtractor={(item) => item.id}
            style={styles.deviceList}
            showsVerticalScrollIndicator={false}
          />
        </View>
      ) : selectedLocation.length > 0 ? (
        <View style={styles.emptyState}>
          <Ionicons name="warning-outline" size={64} color={theme.colors.textSecondary} />
          <Text style={styles.emptyStateText}>该位置下没有设备</Text>
          <Text style={styles.emptyStateSubtext}>请选择其他位置或检查设备注册情况</Text>
        </View>
      ) : (
        <View style={styles.emptyState}>
          <Ionicons name="location-outline" size={64} color={theme.colors.textSecondary} />
          <Text style={styles.emptyStateText}>请先选择基础设施</Text>
          <Text style={styles.emptyStateSubtext}>选择位置后将显示该区域的设备列表</Text>
        </View>
      )}

      {/* 批量操作按钮 */}
      {selectedDevices.length > 0 && (
        <View style={styles.operationSection}>
          <Text style={styles.operationTitle}>
            已选择 {selectedDevices.length} 台设备
          </Text>
          <View style={styles.operationButtons}>
            <Button
              variant="outline"
              size="small"
              onPress={() => handleBatchOperation('transition')}
              loading={isLoading}
              containerStyle={styles.operationButton}
            >
              <Text style={styles.operationButtonText}>过渡模式</Text>
            </Button>
            <Button
              variant="outline"
              size="small"
              onPress={() => handleBatchOperation('restart')}
              loading={isLoading}
              containerStyle={styles.operationButton}
            >
              <Text style={styles.operationButtonText}>远程重启</Text>
            </Button>
            <Button
              variant="outline"
              size="small"
              onPress={() => handleBatchOperation('report')}
              loading={isLoading}
              containerStyle={styles.operationButton}
            >
              <Text style={styles.operationButtonText}>上报数据</Text>
            </Button>
            <Button
              variant="outline"
              size="small"
              onPress={() => handleBatchOperation('sync')}
              loading={isLoading}
              containerStyle={styles.operationButton}
            >
              <Text style={styles.operationButtonText}>下发数据</Text>
            </Button>
          </View>
        </View>
      )}

      {/* 位置选择器 */}
      <LocationPicker
        visible={showLocationPicker}
        title="选择基础设施"
        data={mockLocationData}
        onSelect={handleLocationSelect}
        onCancel={() => setShowLocationPicker(false)}
        maxLevel={3}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  filterSection: {
    backgroundColor: theme.colors.white,
    padding: theme.spacing.lg,
    marginTop: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.lg,
  },
  filterContainer: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  filterButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  filterButtonText: {
    fontSize: 14,
    color: theme.colors.primary,
    fontWeight: '600',
    flex: 1,
  },
  clearButton: {
    paddingHorizontal: theme.spacing.lg,
  },
  clearButtonText: {
    fontSize: 14,
    color: theme.colors.primary,
    fontWeight: '600',
  },
  deviceListSection: {
    flex: 1,
    backgroundColor: theme.colors.white,
    marginTop: theme.spacing.md,
  },
  listHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  selectAllButton: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.sm,
    backgroundColor: theme.colors.surface,
  },
  selectAllText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.primary,
  },
  deviceList: {
    flex: 1,
  },
  deviceItem: {
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.white,
  },
  deviceItemSelected: {
    backgroundColor: theme.colors.primaryLight,
  },
  deviceItemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  deviceItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: theme.spacing.sm,
  },
  deviceLocation: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    flex: 1,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: theme.colors.border,
    backgroundColor: theme.colors.white,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxSelected: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  deviceItemContent: {
    gap: theme.spacing.md,
  },
  deviceInfo: {
    gap: theme.spacing.xs,
  },
  deviceNumber: {
    fontSize: 14,
    color: theme.colors.text,
  },
  deviceStatus: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  deviceVersion: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  deviceTime: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  deviceStats: {
    gap: theme.spacing.xs,
  },
  statItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontWeight: '600',
  },
  statValue: {
    fontSize: 14,
    color: theme.colors.text,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: theme.spacing.xl * 2,
    gap: theme.spacing.md,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  operationSection: {
    backgroundColor: theme.colors.white,
    padding: theme.spacing.lg,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  operationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.md,
  },
  operationButtons: {
    flexDirection: 'row',
    gap: theme.spacing.sm,
  },
  operationButton: {
    flex: 1,
  },
  operationButtonText: {
    fontSize: 12,
    color: theme.colors.primary,
    fontWeight: '600',
  },
});
