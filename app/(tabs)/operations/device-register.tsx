import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
    Al<PERSON>,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { LocationPath, LocationPicker, QRScanner } from '@/components/business';
import { Button, Input } from '@/components/ui';
import { theme } from '@/config/theme';
import { mockFacilityTypes, mockLocationData } from '@/data/mockData';

type DeviceType = 'door' | 'gateway' | 'caller';

interface DeviceRegistrationForm {
  deviceNumber: string;
  facilityType: string;
  location: LocationPath[];
  openToResidents: boolean;
  residentScope: LocationPath[];
  remark: string;
}

export default function DeviceRegisterScreen() {
  const [selectedDeviceType, setSelectedDeviceType] = useState<DeviceType>('door');
  const [showQRScanner, setShowQRScanner] = useState(false);
  const [showLocationPicker, setShowLocationPicker] = useState(false);
  const [showResidentScopePicker, setShowResidentScopePicker] = useState(false);
  const [showFacilityTypePicker, setShowFacilityTypePicker] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const [form, setForm] = useState<DeviceRegistrationForm>({
    deviceNumber: '',
    facilityType: '',
    location: [],
    openToResidents: false,
    residentScope: [],
    remark: '',
  });

  // 设备类型选项
  const deviceTypes = [
    { type: 'door' as DeviceType, name: '门禁设备', icon: 'lock-closed' },
    { type: 'gateway' as DeviceType, name: '网关设备', icon: 'wifi' },
    { type: 'caller' as DeviceType, name: '呼叫器设备', icon: 'call' },
  ];

  // 处理二维码扫描结果
  const handleQRScanSuccess = (data: string) => {
    setForm(prev => ({ ...prev, deviceNumber: data }));
    setShowQRScanner(false);
  };

  // 处理位置选择
  const handleLocationSelect = (path: LocationPath[]) => {
    setForm(prev => ({ ...prev, location: path }));
    setShowLocationPicker(false);
  };

  // 处理住户使用范围选择
  const handleResidentScopeSelect = (path: LocationPath[]) => {
    setForm(prev => ({ ...prev, residentScope: path }));
    setShowResidentScopePicker(false);
  };

  // 处理功能设施类型选择
  const handleFacilityTypeSelect = (facilityType: string) => {
    setForm(prev => ({ ...prev, facilityType }));
    setShowFacilityTypePicker(false);
  };

  // 表单验证
  const validateForm = (): string | null => {
    if (!form.deviceNumber.trim()) {
      return '请扫码设备二维码获取设备编号';
    }

    if (selectedDeviceType === 'door' && !form.facilityType) {
      return '请选择功能设施类型';
    }

    if (form.location.length === 0) {
      return '请选择基础设施';
    }

    if (selectedDeviceType === 'door' && form.openToResidents && form.residentScope.length === 0) {
      return '请选择住户可使用范围';
    }

    return null;
  };

  // 提交注册
  const handleSubmit = async () => {
    const error = validateForm();
    if (error) {
      Alert.alert('提示', error);
      return;
    }

    setIsLoading(true);

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1500));

      Alert.alert(
        '注册成功',
        `${deviceTypes.find(t => t.type === selectedDeviceType)?.name}注册成功！`,
        [
          {
            text: '继续注册',
            onPress: () => {
              setForm({
                deviceNumber: '',
                facilityType: '',
                location: [],
                openToResidents: false,
                residentScope: [],
                remark: '',
              });
            },
          },
          {
            text: '完成',
            style: 'default',
          },
        ]
      );
    } catch (error) {
      Alert.alert('错误', '注册失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* 头部 */}
        <View style={styles.header}>
          <Text style={styles.title}>设备注册</Text>
          <Text style={styles.subtitle}>选择设备类型并完成注册信息录入</Text>
        </View>

        {/* 设备类型选择 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>设备类型</Text>
          <View style={styles.deviceTypeContainer}>
            {deviceTypes.map((deviceType) => (
              <TouchableOpacity
                key={deviceType.type}
                style={[
                  styles.deviceTypeCard,
                  selectedDeviceType === deviceType.type && styles.deviceTypeCardSelected,
                ]}
                onPress={() => setSelectedDeviceType(deviceType.type)}
              >
                <Ionicons
                  name={deviceType.icon as any}
                  size={32}
                  color={
                    selectedDeviceType === deviceType.type
                      ? theme.colors.white
                      : theme.colors.primary
                  }
                />
                <Text
                  style={[
                    styles.deviceTypeName,
                    selectedDeviceType === deviceType.type && styles.deviceTypeNameSelected,
                  ]}
                >
                  {deviceType.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* 设备信息录入 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>设备信息</Text>

          {/* 设备编号 */}
          <View style={styles.formItem}>
            <Text style={styles.label}>设备编号</Text>
            <View style={styles.scanContainer}>
              <Input
                value={form.deviceNumber}
                onChangeText={(text) => setForm(prev => ({ ...prev, deviceNumber: text }))}
                placeholder="请扫码获取设备编号"
                containerStyle={styles.scanInput}
                editable={false}
              />
              <Button
                variant="outline"
                size="medium"
                onPress={() => setShowQRScanner(true)}
                containerStyle={styles.scanButton}
              >
                <Ionicons name="qr-code" size={20} color={theme.colors.primary} />
              </Button>
            </View>
          </View>

          {/* 功能设施类型（仅门禁设备） */}
          {selectedDeviceType === 'door' && (
            <View style={styles.formItem}>
              <Text style={styles.label}>功能设施类型</Text>
              <TouchableOpacity
                style={styles.picker}
                onPress={() => setShowFacilityTypePicker(true)}
              >
                <Text style={[styles.pickerText, !form.facilityType && styles.pickerPlaceholder]}>
                  {form.facilityType || '请选择功能设施类型'}
                </Text>
                <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
              </TouchableOpacity>
            </View>
          )}

          {/* 基础设施 */}
          <View style={styles.formItem}>
            <Text style={styles.label}>基础设施</Text>
            <TouchableOpacity
              style={styles.picker}
              onPress={() => setShowLocationPicker(true)}
            >
              <Text style={[styles.pickerText, form.location.length === 0 && styles.pickerPlaceholder]}>
                {form.location.length > 0
                  ? form.location.map(item => item.name).join(' > ')
                  : '请选择基础设施'
                }
              </Text>
              <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          </View>

          {/* 备注 */}
          <View style={styles.formItem}>
            <Text style={styles.label}>备注（选填）</Text>
            <Input
              value={form.remark}
              onChangeText={(text) => setForm(prev => ({ ...prev, remark: text }))}
              placeholder="请输入备注信息"
              multiline
              numberOfLines={3}
            />
          </View>

          {/* 是否开放给住户（仅门禁设备） */}
          {selectedDeviceType === 'door' && (
            <>
              <View style={styles.formItem}>
                <Text style={styles.label}>是否开放给小区住户</Text>
                <View style={styles.switchContainer}>
                  <TouchableOpacity
                    style={[
                      styles.switchOption,
                      form.openToResidents && styles.switchOptionSelected,
                    ]}
                    onPress={() => setForm(prev => ({ ...prev, openToResidents: true, residentScope: [] }))}
                  >
                    <Text
                      style={[
                        styles.switchOptionText,
                        form.openToResidents && styles.switchOptionTextSelected,
                      ]}
                    >
                      是
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      styles.switchOption,
                      !form.openToResidents && styles.switchOptionSelected,
                    ]}
                    onPress={() => setForm(prev => ({ ...prev, openToResidents: false, residentScope: [] }))}
                  >
                    <Text
                      style={[
                        styles.switchOptionText,
                        !form.openToResidents && styles.switchOptionTextSelected,
                      ]}
                    >
                      否
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>

              {/* 住户使用范围 */}
              {form.openToResidents && (
                <View style={styles.formItem}>
                  <Text style={styles.label}>住户使用范围</Text>
                  <TouchableOpacity
                    style={styles.picker}
                    onPress={() => setShowResidentScopePicker(true)}
                  >
                    <Text style={[styles.pickerText, form.residentScope.length === 0 && styles.pickerPlaceholder]}>
                      {form.residentScope.length > 0
                        ? form.residentScope.map(item => item.name).join(' > ')
                        : '请选择住户使用范围'
                      }
                    </Text>
                    <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
                  </TouchableOpacity>
                </View>
              )}
            </>
          )}
        </View>

        {/* 提交按钮 */}
        <View style={styles.footer}>
          <Button
            variant="primary"
            size="large"
            onPress={handleSubmit}
            loading={isLoading}
            containerStyle={styles.submitButton}
          >
            确认注册
          </Button>
        </View>
      </ScrollView>

      {/* 二维码扫描器 */}
      <QRScanner
        visible={showQRScanner}
        onScanSuccess={handleQRScanSuccess}
        onClose={() => setShowQRScanner(false)}
        hint="请将设备二维码放入扫描框内"
      />

      {/* 位置选择器 */}
      <LocationPicker
        visible={showLocationPicker}
        title="选择基础设施"
        data={mockLocationData}
        onSelect={handleLocationSelect}
        onCancel={() => setShowLocationPicker(false)}
        maxLevel={selectedDeviceType === 'caller' ? 1 : 5}
      />

      {/* 住户使用范围选择器 */}
      <LocationPicker
        visible={showResidentScopePicker}
        title="选择住户使用范围"
        data={mockLocationData}
        onSelect={handleResidentScopeSelect}
        onCancel={() => setShowResidentScopePicker(false)}
        maxLevel={5}
      />

      {/* 功能设施类型选择器 */}
      {showFacilityTypePicker && (
        <View style={styles.modalOverlay}>
          <View style={styles.facilityTypeModal}>
            <Text style={styles.modalTitle}>选择功能设施类型</Text>
            {mockFacilityTypes.map((type) => (
              <TouchableOpacity
                key={type.id}
                style={styles.facilityTypeItem}
                onPress={() => handleFacilityTypeSelect(type.name)}
              >
                <Text style={styles.facilityTypeItemText}>{type.name}</Text>
              </TouchableOpacity>
            ))}
            <TouchableOpacity
              style={styles.modalCancelButton}
              onPress={() => setShowFacilityTypePicker(false)}
            >
              <Text style={styles.modalCancelText}>取消</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  section: {
    backgroundColor: theme.colors.white,
    marginTop: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.lg,
  },
  deviceTypeContainer: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  deviceTypeCard: {
    flex: 1,
    alignItems: 'center',
    padding: theme.spacing.lg,
    borderRadius: theme.borderRadius.md,
    borderWidth: 2,
    borderColor: theme.colors.border,
    backgroundColor: theme.colors.white,
  },
  deviceTypeCardSelected: {
    borderColor: theme.colors.primary,
    backgroundColor: theme.colors.primary,
  },
  deviceTypeName: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginTop: theme.spacing.sm,
    textAlign: 'center',
  },
  deviceTypeNameSelected: {
    color: theme.colors.white,
  },
  formItem: {
    marginBottom: theme.spacing.lg,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  scanContainer: {
    flexDirection: 'row',
    gap: theme.spacing.md,
    alignItems: 'flex-end',
  },
  scanInput: {
    flex: 1,
  },
  scanButton: {
    minWidth: 50,
    paddingHorizontal: theme.spacing.md,
  },
  picker: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.white,
  },
  pickerText: {
    fontSize: 16,
    color: theme.colors.text,
    flex: 1,
  },
  pickerPlaceholder: {
    color: theme.colors.textSecondary,
  },
  switchContainer: {
    flexDirection: 'row',
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    overflow: 'hidden',
  },
  switchOption: {
    flex: 1,
    paddingVertical: theme.spacing.md,
    alignItems: 'center',
    backgroundColor: theme.colors.white,
  },
  switchOptionSelected: {
    backgroundColor: theme.colors.primary,
  },
  switchOptionText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
  },
  switchOptionTextSelected: {
    color: theme.colors.white,
  },
  footer: {
    padding: theme.spacing.lg,
    backgroundColor: theme.colors.white,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
  },
  submitButton: {
    width: '100%',
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  facilityTypeModal: {
    backgroundColor: theme.colors.white,
    margin: theme.spacing.lg,
    borderRadius: theme.borderRadius.lg,
    overflow: 'hidden',
    minWidth: 280,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
    textAlign: 'center',
    paddingVertical: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  facilityTypeItem: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  facilityTypeItemText: {
    fontSize: 16,
    color: theme.colors.text,
  },
  modalCancelButton: {
    paddingVertical: theme.spacing.lg,
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
  },
  modalCancelText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
});
