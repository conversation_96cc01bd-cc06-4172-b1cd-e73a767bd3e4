/**
 * 应用根布局
 * 集成Tamagui、认证状态管理和版本更新功能
 */

import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect } from 'react';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import 'react-native-reanimated';

import { UpdateChecker } from '@/components/common/update';
import { TamaguiProvider } from '@/components/providers';
import { useAppStore } from '@/lib/stores/app.store';
import { useAuthStore } from '@/lib/stores/auth.store';

export default function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  const { markLaunched } = useAppStore();
  const { initializeAuth } = useAuthStore();

  // 标记应用已启动和初始化认证状态
  useEffect(() => {
    markLaunched();
    initializeAuth();
  }, [markLaunched, initializeAuth]);

  if (!loaded) {
    return null;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <TamaguiProvider>
        <Stack
          screenOptions={{
            headerShown: false,
            animation: 'slide_from_right',
          }}
        >
          <Stack.Screen name="index" />
          <Stack.Screen name="(auth)" />
          <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
          <Stack.Screen name="+not-found" />
        </Stack>

        {/* 版本更新检查 */}
        <UpdateChecker autoCheck={true} />

        <StatusBar style="auto" />
      </TamaguiProvider>
    </GestureHandlerRootView>
  );
}
