import { TamaguiProvider as TamaguiProviderBase } from '@tamagui/core';
import { PortalProvider } from '@tamagui/portal';
import React from 'react';
import config from '../../tamagui.config';

interface TamaguiProviderProps {
  children: React.ReactNode;
}

export const TamaguiProvider: React.FC<TamaguiProviderProps> = ({ children }) => {
  return (
    <TamaguiProviderBase config={config}>
      <PortalProvider>
        {children}
      </PortalProvider>
    </TamaguiProviderBase>
  );
};
