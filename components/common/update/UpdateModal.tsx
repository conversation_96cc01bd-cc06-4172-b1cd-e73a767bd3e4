/**
 * 版本更新模态框组件
 * T016: App版本检查
 * T017: 下载更新功能
 */

import React from 'react';
import {
  YStack,
  XStack,
  Text,
  Button,
  ScrollView,
  Progress,
  styled,
  Sheet,
} from 'tamagui';
import { Ionicons } from '@expo/vector-icons';
import { useAppStore } from '@/lib/stores/app.store';

export interface UpdateModalProps {
  visible: boolean;
  version: string;
  description: string;
  forceUpdate?: boolean;
  downloading?: boolean;
  onUpdate: () => void;
  onSkip: () => void;
  onCancel: () => void;
}

// 样式化组件
const UpdateContainer = styled(YStack, {
  padding: '$4',
  gap: '$4',
  backgroundColor: '$background',
  borderTopLeftRadius: '$6',
  borderTopRightRadius: '$6',
  maxHeight: '80%',
});

const HeaderContainer = styled(YStack, {
  alignItems: 'center',
  gap: '$3',
  paddingBottom: '$4',
  borderBottomWidth: 1,
  borderBottomColor: '$borderColor',
});

const IconContainer = styled(YStack, {
  width: 60,
  height: 60,
  borderRadius: 30,
  backgroundColor: '$primary',
  alignItems: 'center',
  justifyContent: 'center',
});

const ContentContainer = styled(YStack, {
  gap: '$3',
  flex: 1,
});

const VersionContainer = styled(XStack, {
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: '$3',
  backgroundColor: '$backgroundHover',
  borderRadius: '$4',
});

const DescriptionContainer = styled(YStack, {
  gap: '$2',
  maxHeight: 200,
});

const ProgressContainer = styled(YStack, {
  gap: '$2',
  padding: '$3',
  backgroundColor: '$backgroundHover',
  borderRadius: '$4',
});

const ButtonContainer = styled(XStack, {
  gap: '$3',
  paddingTop: '$4',
  borderTopWidth: 1,
  borderTopColor: '$borderColor',
});

const ForceUpdateBadge = styled(XStack, {
  alignItems: 'center',
  gap: '$1',
  paddingHorizontal: '$2',
  paddingVertical: '$1',
  backgroundColor: '$error',
  borderRadius: '$2',
});

/**
 * 版本更新模态框组件
 */
export const UpdateModal: React.FC<UpdateModalProps> = ({
  visible,
  version,
  description,
  forceUpdate = false,
  downloading = false,
  onUpdate,
  onSkip,
  onCancel,
}) => {
  const { updateProgress, currentVersion } = useAppStore();

  // 格式化更新描述
  const formatDescription = (desc: string) => {
    return desc.split('\n').map((line, index) => (
      <Text key={index} fontSize="$3" color="$color" lineHeight="$1">
        {line}
      </Text>
    ));
  };

  return (
    <Sheet
      modal
      open={visible}
      onOpenChange={(open) => {
        if (!open && !forceUpdate) {
          onCancel();
        }
      }}
      snapPoints={[85]}
      dismissOnSnapToBottom={!forceUpdate}
      dismissOnOverlayPress={!forceUpdate}
    >
      <Sheet.Overlay />
      <Sheet.Handle />
      <Sheet.Frame>
        <UpdateContainer>
          {/* 头部 */}
          <HeaderContainer>
            <IconContainer>
              <Ionicons name="download" size={24} color="white" />
            </IconContainer>
            
            <YStack alignItems="center" gap="$1">
              <Text fontSize="$6" fontWeight="bold" color="$color">
                发现新版本
              </Text>
              {forceUpdate && (
                <ForceUpdateBadge>
                  <Ionicons name="warning" size={12} color="white" />
                  <Text fontSize="$2" color="white" fontWeight="500">
                    强制更新
                  </Text>
                </ForceUpdateBadge>
              )}
            </YStack>
          </HeaderContainer>

          {/* 内容 */}
          <ContentContainer>
            {/* 版本信息 */}
            <VersionContainer>
              <YStack>
                <Text fontSize="$3" color="$placeholderColor">
                  当前版本
                </Text>
                <Text fontSize="$4" fontWeight="500" color="$color">
                  v{currentVersion}
                </Text>
              </YStack>
              
              <Ionicons name="arrow-forward" size={20} color="#999" />
              
              <YStack alignItems="flex-end">
                <Text fontSize="$3" color="$placeholderColor">
                  最新版本
                </Text>
                <Text fontSize="$4" fontWeight="500" color="$primary">
                  v{version}
                </Text>
              </YStack>
            </VersionContainer>

            {/* 更新描述 */}
            <DescriptionContainer>
              <Text fontSize="$4" fontWeight="500" color="$color">
                更新内容
              </Text>
              <ScrollView
                showsVerticalScrollIndicator={false}
                style={{ maxHeight: 150 }}
              >
                <YStack gap="$1">
                  {formatDescription(description)}
                </YStack>
              </ScrollView>
            </DescriptionContainer>

            {/* 下载进度 */}
            {downloading && (
              <ProgressContainer>
                <XStack justifyContent="space-between" alignItems="center">
                  <Text fontSize="$3" color="$color">
                    下载进度
                  </Text>
                  <Text fontSize="$3" color="$primary" fontWeight="500">
                    {updateProgress}%
                  </Text>
                </XStack>
                <Progress value={updateProgress} max={100}>
                  <Progress.Indicator animation="bouncy" />
                </Progress>
              </ProgressContainer>
            )}
          </ContentContainer>

          {/* 操作按钮 */}
          <ButtonContainer>
            {!forceUpdate && !downloading && (
              <Button
                flex={1}
                variant="outlined"
                onPress={onSkip}
                borderColor="$borderColor"
                backgroundColor="transparent"
              >
                <Text color="$placeholderColor">
                  稍后提醒
                </Text>
              </Button>
            )}
            
            <Button
              flex={forceUpdate || downloading ? 1 : 2}
              backgroundColor="$primary"
              onPress={onUpdate}
              disabled={downloading}
              opacity={downloading ? 0.6 : 1}
            >
              <XStack alignItems="center" gap="$2">
                {downloading && (
                  <Ionicons name="download" size={16} color="white" />
                )}
                <Text color="white" fontWeight="500">
                  {downloading ? '下载中...' : '立即更新'}
                </Text>
              </XStack>
            </Button>
          </ButtonContainer>
        </UpdateContainer>
      </Sheet.Frame>
    </Sheet>
  );
};
