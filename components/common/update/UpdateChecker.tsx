/**
 * 版本更新检查组件
 * T016: App版本检查
 * T017: 下载更新功能
 */

import React, { useEffect } from 'react';
import { Alert, Linking, Platform } from 'react-native';
import { useAppStore } from '@/lib/stores/app.store';
import { UpdateModal } from './UpdateModal';

export interface UpdateCheckerProps {
  autoCheck?: boolean;
  checkInterval?: number;
  showModal?: boolean;
}

/**
 * 版本更新检查组件
 * 自动检查版本更新并显示更新提示
 */
export const UpdateChecker: React.FC<UpdateCheckerProps> = ({
  autoCheck = true,
  checkInterval = 24 * 60 * 60 * 1000, // 24小时
  showModal = true,
}) => {
  const {
    updateStatus,
    latestVersion,
    updateError,
    checkForUpdates,
    downloadUpdate,
    installUpdate,
    skipUpdate,
    clearUpdateError,
    lastLaunchTime,
  } = useAppStore();

  // 自动检查更新
  useEffect(() => {
    if (!autoCheck) return;

    const shouldCheck = () => {
      if (!lastLaunchTime) return true;
      
      const lastCheck = new Date(lastLaunchTime);
      const now = new Date();
      const timeDiff = now.getTime() - lastCheck.getTime();
      
      return timeDiff >= checkInterval;
    };

    if (shouldCheck()) {
      checkForUpdates().catch(console.error);
    }
  }, [autoCheck, checkInterval, lastLaunchTime, checkForUpdates]);

  // 处理更新错误
  useEffect(() => {
    if (updateError) {
      Alert.alert(
        '更新失败',
        updateError,
        [
          { text: '确定', onPress: clearUpdateError },
        ]
      );
    }
  }, [updateError, clearUpdateError]);

  // 处理强制更新
  useEffect(() => {
    if (latestVersion?.forceUpdate && updateStatus === 'available') {
      Alert.alert(
        '强制更新',
        `发现新版本 ${latestVersion.version}，此版本为强制更新，请立即更新。`,
        [
          {
            text: '立即更新',
            onPress: handleUpdate,
          },
        ],
        { cancelable: false }
      );
    }
  }, [latestVersion, updateStatus]);

  // 处理更新
  const handleUpdate = async () => {
    if (!latestVersion) return;

    try {
      if (Platform.OS === 'android' && latestVersion.updateUrl) {
        // Android: 下载APK
        await downloadUpdate();
        
        Alert.alert(
          '下载完成',
          '更新包下载完成，是否立即安装？',
          [
            { text: '稍后安装', style: 'cancel' },
            { text: '立即安装', onPress: installUpdate },
          ]
        );
      } else {
        // iOS: 跳转到App Store
        const appStoreUrl = latestVersion.updateUrl || 'https://apps.apple.com/app/id123456789';
        const supported = await Linking.canOpenURL(appStoreUrl);
        
        if (supported) {
          await Linking.openURL(appStoreUrl);
        } else {
          Alert.alert('错误', '无法打开App Store');
        }
      }
    } catch (error) {
      console.error('Update failed:', error);
    }
  };

  // 跳过更新
  const handleSkipUpdate = () => {
    if (latestVersion) {
      skipUpdate(latestVersion.version);
    }
  };

  // 手动检查更新
  const handleManualCheck = async () => {
    try {
      const update = await checkForUpdates();
      
      if (!update) {
        Alert.alert('检查更新', '当前已是最新版本');
      }
    } catch (error) {
      // 错误已在effect中处理
    }
  };

  if (!showModal) {
    return null;
  }

  return (
    <UpdateModal
      visible={updateStatus === 'available' && !latestVersion?.forceUpdate}
      version={latestVersion?.version || ''}
      description={latestVersion?.updateDescription || ''}
      forceUpdate={latestVersion?.forceUpdate || false}
      downloading={updateStatus === 'downloading'}
      onUpdate={handleUpdate}
      onSkip={handleSkipUpdate}
      onCancel={handleSkipUpdate}
    />
  );
};

/**
 * 手动检查更新Hook
 */
export const useUpdateChecker = () => {
  const {
    updateStatus,
    latestVersion,
    updateError,
    updateProgress,
    checkForUpdates,
    downloadUpdate,
    installUpdate,
    skipUpdate,
    clearUpdateError,
  } = useAppStore();

  const manualCheck = async () => {
    try {
      const update = await checkForUpdates();
      return update;
    } catch (error) {
      throw error;
    }
  };

  const update = async () => {
    try {
      if (Platform.OS === 'android') {
        await downloadUpdate();
        return 'downloaded';
      } else {
        // iOS跳转到App Store
        if (latestVersion?.updateUrl) {
          const supported = await Linking.canOpenURL(latestVersion.updateUrl);
          if (supported) {
            await Linking.openURL(latestVersion.updateUrl);
            return 'redirected';
          }
        }
        throw new Error('无法打开App Store');
      }
    } catch (error) {
      throw error;
    }
  };

  const install = async () => {
    try {
      await installUpdate();
      return true;
    } catch (error) {
      throw error;
    }
  };

  const skip = () => {
    if (latestVersion) {
      skipUpdate(latestVersion.version);
    }
  };

  return {
    updateStatus,
    latestVersion,
    updateError,
    updateProgress,
    manualCheck,
    update,
    install,
    skip,
    clearError: clearUpdateError,
  };
};
