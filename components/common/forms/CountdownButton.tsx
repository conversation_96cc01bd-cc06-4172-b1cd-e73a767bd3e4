/**
 * 倒计时按钮组件
 * 常用于验证码发送等场景
 */

import { CustomButton, CustomButtonProps } from '@/components/common/buttons';
import React, { useEffect, useRef, useState } from 'react';
import { Text, styled } from 'tamagui';

// 倒计时按钮组件属性
export interface CountdownButtonProps extends Omit<CustomButtonProps, 'children' | 'loading' | 'disabled'> {
  countdown?: number; // 倒计时秒数，默认60秒
  text?: string; // 默认文本
  countdownText?: string; // 倒计时文本模板，{count}会被替换为剩余秒数
  disabledText?: string; // 禁用状态文本
  onPress?: () => Promise<boolean> | boolean | void; // 点击事件，返回true开始倒计时
  disabled?: boolean;
  autoStart?: boolean; // 是否自动开始倒计时
  resetOnComplete?: boolean; // 倒计时完成后是否重置状态
}

// 样式化倒计时文本
const CountdownText = styled(Text, {
  name: 'CountdownText',
  fontSize: '$4',
  fontWeight: '500',
  
  variants: {
    variant: {
      primary: {
        color: '$white',
      },
      
      secondary: {
        color: '$white',
      },
      
      outline: {
        color: '$primary',
      },
      
      ghost: {
        color: '$primary',
      },
      
      danger: {
        color: '$white',
      },
    },
    
    disabled: {
      true: {
        color: '$placeholderColor',
      },
    },
  } as const,
  
  defaultVariants: {
    variant: 'primary',
  },
});

export const CountdownButton: React.FC<CountdownButtonProps> = ({
  countdown = 60,
  text = '获取验证码',
  countdownText = '{count}秒后重试',
  disabledText = '获取验证码',
  onPress,
  disabled = false,
  autoStart = false,
  resetOnComplete = true,
  variant = 'primary',
  size = 'medium',
  ...props
}) => {
  const [isCountingDown, setIsCountingDown] = useState(false);
  const [remainingTime, setRemainingTime] = useState(countdown);
  const [isLoading, setIsLoading] = useState(false);
  const timerRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // 清理定时器
  const clearTimer = () => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
  };

  // 开始倒计时
  const startCountdown = () => {
    setIsCountingDown(true);
    setRemainingTime(countdown);
    
    const timer = () => {
      setRemainingTime(prev => {
        if (prev <= 1) {
          setIsCountingDown(false);
          if (resetOnComplete) {
            return countdown;
          }
          return 0;
        }
        
        timerRef.current = setTimeout(timer, 1000);
        return prev - 1;
      });
    };
    
    timerRef.current = setTimeout(timer, 1000);
  };

  // 停止倒计时（暴露给外部使用）
  // const stopCountdown = () => {
  //   clearTimer();
  //   setIsCountingDown(false);
  //   setRemainingTime(countdown);
  // };

  // 重置倒计时（暴露给外部使用）
  // const resetCountdown = () => {
  //   clearTimer();
  //   setIsCountingDown(false);
  //   setRemainingTime(countdown);
  // };

  // 处理按钮点击
  const handlePress = async () => {
    if (disabled || isCountingDown || isLoading) {
      return;
    }

    try {
      setIsLoading(true);
      
      if (onPress) {
        const result = await onPress();
        
        // 如果返回true或者没有返回值，开始倒计时
        if (result === true || result === undefined) {
          startCountdown();
        }
      } else {
        // 如果没有onPress处理函数，直接开始倒计时
        startCountdown();
      }
    } catch (error) {
      console.error('CountdownButton onPress error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 自动开始倒计时
  useEffect(() => {
    if (autoStart) {
      startCountdown();
    }
    
    return () => {
      clearTimer();
    };
  }, [autoStart]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      clearTimer();
    };
  }, []);

  // 获取显示文本
  const getDisplayText = (): string => {
    if (disabled && !isCountingDown) {
      return disabledText;
    }
    
    if (isCountingDown) {
      return countdownText.replace('{count}', remainingTime.toString());
    }
    
    return text;
  };

  // 判断按钮是否应该禁用
  const isButtonDisabled = disabled || isCountingDown || isLoading;

  const displayText = getDisplayText();

  return (
    <CustomButton
      {...props}
      variant={variant}
      size={size}
      disabled={isButtonDisabled}
      loading={isLoading}
      onPress={handlePress}
    >
      <CountdownText
        variant={variant}
        disabled={isButtonDisabled}
      >
        {displayText}
      </CountdownText>
    </CustomButton>
  );
};

// 倒计时按钮的Hook，用于外部控制
export const useCountdownButton = (initialCountdown = 60) => {
  const [isCountingDown, setIsCountingDown] = useState(false);
  const [remainingTime, setRemainingTime] = useState(initialCountdown);
  const timerRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const clearTimer = () => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
  };

  const start = () => {
    setIsCountingDown(true);
    setRemainingTime(initialCountdown);
    
    const timer = () => {
      setRemainingTime(prev => {
        if (prev <= 1) {
          setIsCountingDown(false);
          return initialCountdown;
        }
        
        timerRef.current = setTimeout(timer, 1000);
        return prev - 1;
      });
    };
    
    timerRef.current = setTimeout(timer, 1000);
  };

  const stop = () => {
    clearTimer();
    setIsCountingDown(false);
    setRemainingTime(initialCountdown);
  };

  const reset = () => {
    clearTimer();
    setIsCountingDown(false);
    setRemainingTime(initialCountdown);
  };

  useEffect(() => {
    return () => {
      clearTimer();
    };
  }, []);

  return {
    isCountingDown,
    remainingTime,
    start,
    stop,
    reset,
  };
};

// 验证码倒计时按钮的便捷组件
export interface VerificationCodeButtonProps extends Omit<CountdownButtonProps, 'text' | 'countdownText'> {
  phoneNumber?: string;
  onSendCode?: (phoneNumber?: string) => Promise<boolean | void> | boolean | void;
}

export const VerificationCodeButton: React.FC<VerificationCodeButtonProps> = ({
  phoneNumber,
  onSendCode,
  ...props
}) => {
  const handlePress = async (): Promise<boolean> => {
    if (onSendCode) {
      const result = await onSendCode(phoneNumber);
      return result === true;
    }
    return true;
  };

  return (
    <CountdownButton
      {...props}
      text="获取验证码"
      countdownText="{count}秒后重新获取"
      disabledText="获取验证码"
      onPress={handlePress}
    />
  );
};
