/**
 * 权限守卫组件
 * T015: 权限控制系统
 */

import React from 'react';
import { YStack, Text, styled } from 'tamagui';
import { useAuthStore } from '@/lib/stores';
import { hasPermission, hasAnyPermission, hasAllPermissions } from '@/lib/utils/permissions';

// 权限守卫组件属性
export interface PermissionGuardProps {
  children: React.ReactNode;
  permission?: string;
  permissions?: string[];
  requireAll?: boolean;
  fallback?: React.ReactNode;
  showFallback?: boolean;
}

// 样式化组件
const NoPermissionContainer = styled(YStack, {
  alignItems: 'center',
  justifyContent: 'center',
  padding: '$4',
  backgroundColor: '$backgroundHover',
  borderRadius: '$4',
  borderWidth: 1,
  borderColor: '$borderColor',
  borderStyle: 'dashed',
});

const NoPermissionText = styled(Text, {
  fontSize: '$3',
  color: '$placeholderColor',
  textAlign: 'center',
});

/**
 * 权限守卫组件
 * 根据用户权限决定是否渲染子组件
 */
export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permission,
  permissions = [],
  requireAll = false,
  fallback,
  showFallback = true,
}) => {
  const { user } = useAuthStore();

  // 检查权限
  const hasAccess = React.useMemo(() => {
    if (!user) {
      return false;
    }

    // 单个权限检查
    if (permission) {
      return hasPermission(user, permission);
    }

    // 多个权限检查
    if (permissions.length > 0) {
      return requireAll 
        ? hasAllPermissions(user, permissions)
        : hasAnyPermission(user, permissions);
    }

    // 没有指定权限要求，默认允许
    return true;
  }, [user, permission, permissions, requireAll]);

  // 如果有权限，渲染子组件
  if (hasAccess) {
    return <>{children}</>;
  }

  // 如果有自定义fallback，渲染fallback
  if (fallback) {
    return <>{fallback}</>;
  }

  // 如果不显示fallback，返回null
  if (!showFallback) {
    return null;
  }

  // 默认的无权限提示
  return (
    <NoPermissionContainer>
      <NoPermissionText>
        您没有权限访问此功能
      </NoPermissionText>
    </NoPermissionContainer>
  );
};

/**
 * 权限检查Hook
 * 用于在组件中检查权限
 */
export const usePermission = () => {
  const { user } = useAuthStore();

  return React.useMemo(() => ({
    hasPermission: (permission: string) => hasPermission(user, permission),
    hasAnyPermission: (permissions: string[]) => hasAnyPermission(user, permissions),
    hasAllPermissions: (permissions: string[]) => hasAllPermissions(user, permissions),
    user,
  }), [user]);
};

/**
 * 权限按钮组件
 * 根据权限控制按钮的显示和禁用状态
 */
export interface PermissionButtonProps {
  children: React.ReactNode;
  permission?: string;
  permissions?: string[];
  requireAll?: boolean;
  onPress?: () => void;
  disabled?: boolean;
  hideWhenNoPermission?: boolean;
  [key: string]: any;
}

export const PermissionButton: React.FC<PermissionButtonProps> = ({
  children,
  permission,
  permissions = [],
  requireAll = false,
  onPress,
  disabled = false,
  hideWhenNoPermission = false,
  ...props
}) => {
  const { hasPermission: checkPermission, hasAnyPermission, hasAllPermissions } = usePermission();

  // 检查权限
  const hasAccess = React.useMemo(() => {
    // 单个权限检查
    if (permission) {
      return checkPermission(permission);
    }

    // 多个权限检查
    if (permissions.length > 0) {
      return requireAll 
        ? hasAllPermissions(permissions)
        : hasAnyPermission(permissions);
    }

    // 没有指定权限要求，默认允许
    return true;
  }, [permission, permissions, requireAll, checkPermission, hasAnyPermission, hasAllPermissions]);

  // 如果没有权限且需要隐藏，返回null
  if (!hasAccess && hideWhenNoPermission) {
    return null;
  }

  // 动态导入Button组件
  const { CustomButton } = require('@/components/common/buttons');

  return (
    <CustomButton
      {...props}
      onPress={hasAccess ? onPress : undefined}
      disabled={disabled || !hasAccess}
      opacity={hasAccess ? 1 : 0.5}
    >
      {children}
    </CustomButton>
  );
};

/**
 * 权限文本组件
 * 根据权限控制文本的显示
 */
export interface PermissionTextProps {
  children: React.ReactNode;
  permission?: string;
  permissions?: string[];
  requireAll?: boolean;
  fallbackText?: string;
  hideWhenNoPermission?: boolean;
  [key: string]: any;
}

export const PermissionText: React.FC<PermissionTextProps> = ({
  children,
  permission,
  permissions = [],
  requireAll = false,
  fallbackText = '无权限',
  hideWhenNoPermission = false,
  ...props
}) => {
  const { hasPermission: checkPermission, hasAnyPermission, hasAllPermissions } = usePermission();

  // 检查权限
  const hasAccess = React.useMemo(() => {
    // 单个权限检查
    if (permission) {
      return checkPermission(permission);
    }

    // 多个权限检查
    if (permissions.length > 0) {
      return requireAll 
        ? hasAllPermissions(permissions)
        : hasAnyPermission(permissions);
    }

    // 没有指定权限要求，默认允许
    return true;
  }, [permission, permissions, requireAll, checkPermission, hasAnyPermission, hasAllPermissions]);

  // 如果没有权限且需要隐藏，返回null
  if (!hasAccess && hideWhenNoPermission) {
    return null;
  }

  return (
    <Text {...props}>
      {hasAccess ? children : fallbackText}
    </Text>
  );
};
