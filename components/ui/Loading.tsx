import React from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';
import { Text } from 'tamagui';

interface LoadingProps {
  visible: boolean;
  text?: string;
  size?: 'small' | 'large';
  color?: string;
}

export function Loading({ 
  visible, 
  text = '加载中...', 
  size = 'large',
  color = '#FF6B35' 
}: LoadingProps) {
  if (!visible) return null;

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <ActivityIndicator size={size} color={color} />
        {text && (
          <Text 
            fontSize="$4" 
            color="$color" 
            marginTop="$3"
            textAlign="center"
          >
            {text}
          </Text>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
  },
  content: {
    alignItems: 'center',
    padding: 20,
  },
});
