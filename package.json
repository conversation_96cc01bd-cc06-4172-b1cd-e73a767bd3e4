{"name": "youjiapm", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@hookform/resolvers": "^5.1.1", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@react-navigation/native-stack": "^7.3.10", "@react-navigation/stack": "^7.3.3", "@tamagui/animations-react-native": "^1.126.14", "@tamagui/config": "^1.126.14", "@tamagui/core": "^1.126.14", "@tamagui/font-inter": "^1.126.14", "@tamagui/theme-base": "^1.126.14", "@tanstack/react-query": "^5.80.6", "axios": "^1.9.0", "date-fns": "^4.1.0", "expo": "~53.0.10", "expo-blur": "~14.1.5", "expo-camera": "~16.1.8", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.2.1", "expo-image-picker": "^16.1.4", "expo-linking": "~7.1.5", "expo-notifications": "^0.31.3", "expo-router": "~5.0.7", "expo-secure-store": "^14.2.3", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.8", "expo-web-browser": "~14.1.6", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.57.0", "react-native": "0.79.3", "react-native-gesture-handler": "~2.24.0", "react-native-image-crop-picker": "^0.50.1", "react-native-keychain": "^10.0.0", "react-native-nfc-manager": "^3.16.1", "react-native-permissions": "^5.4.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "tamagui": "^1.126.14", "tslib": "^2.8.1", "yup": "^1.6.1", "zustand": "^5.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}